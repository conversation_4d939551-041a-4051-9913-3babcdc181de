import 'package:equatable/equatable.dart';

class RecentScan extends Equatable {
  final String id;
  final String userId;
  final String scanType;
  final String target;
  final String sr; // Security Rating field from backend
  final ScanResult result;
  final DateTime createdAt;

  const RecentScan({
    required this.id,
    required this.userId,
    required this.scanType,
    required this.target,
    required this.sr,
    required this.result,
    required this.createdAt,
  });

  factory RecentScan.fromJson(Map<String, dynamic> json) {
    return RecentScan(
      id: json['id'] as String,
      userId: json['userId'] as String,
      scanType: json['scanType'] as String,
      target: json['target'] as String,
      sr: json['SR'] as String, // Note: backend uses 'SR' (uppercase)
      result: ScanResult.fromJson(json['result'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'scanType': scanType,
      'target': target,
      'SR': sr, // Backend expects 'SR' (uppercase)
      'result': result.toJson(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  RecentScan copyWith({
    String? id,
    String? userId,
    String? scanType,
    String? target,
    String? sr,
    ScanResult? result,
    DateTime? createdAt,
  }) {
    return RecentScan(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      scanType: scanType ?? this.scanType,
      target: target ?? this.target,
      sr: sr ?? this.sr,
      result: result ?? this.result,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    scanType,
    target,
    sr,
    result,
    createdAt,
  ];
}

class ScanResult extends Equatable {
  final double threatScore;
  final String threatLevel;
  final double confidence;
  final List<Finding> findings;

  const ScanResult({
    required this.threatScore,
    required this.threatLevel,
    required this.confidence,
    required this.findings,
  });

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      threatScore: (json['threatScore'] as num).toDouble(),
      threatLevel: json['threatLevel'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      findings:
          (json['findings'] as List<dynamic>)
              .map(
                (finding) => Finding.fromJson(finding as Map<String, dynamic>),
              )
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'threatScore': threatScore,
      'threatLevel': threatLevel,
      'confidence': confidence,
      'findings': findings.map((finding) => finding.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [threatScore, threatLevel, confidence, findings];
}

class Finding extends Equatable {
  final String type;
  final String severity;
  final String description;

  const Finding({
    required this.type,
    required this.severity,
    required this.description,
  });

  factory Finding.fromJson(Map<String, dynamic> json) {
    return Finding(
      type: json['type'] as String,
      severity: json['severity'] as String,
      description: json['description'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'type': type, 'severity': severity, 'description': description};
  }

  @override
  List<Object?> get props => [type, severity, description];
}
