import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:darkops/dashboard/qr_code_scanner.dart';
import 'package:darkops/dashboard/sms_analyzer.dart';
import 'package:darkops/dashboard/email_analyzer.dart';
import 'package:darkops/dashboard/url_scanner.dart';
import 'package:darkops/dashboard/apk_analyzer.dart';
import 'package:darkops/screens/login_options.dart';
import 'package:darkops/dashboard/theme_provider.dart';
import 'package:darkops/blocs/auth/auth_bloc.dart';
import 'package:darkops/blocs/dashboard/dashboard_bloc.dart';
import 'package:darkops/blocs/dashboard/dashboard_event.dart';
import 'package:darkops/blocs/dashboard/dashboard_state.dart';
import 'package:darkops/models/dashboard/dashboard_stats.dart';
import 'package:provider/provider.dart';

enum FeatureType { sms, email, url, qr, apk }

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    // Request dashboard data when the page loads
    context.read<DashboardBloc>().add(const DashboardDataRequested());
  }

  final Map<FeatureType, IconData> featureIcons = {
    FeatureType.sms: Icons.sms_outlined,
    FeatureType.email: Icons.email_outlined,
    FeatureType.url: Icons.language_outlined,
    FeatureType.qr: Icons.qr_code_2_outlined,
    FeatureType.apk: Icons.android_outlined,
  };

  final Map<FeatureType, String> featureTitles = {
    FeatureType.sms: "SMS Scans",
    FeatureType.email: "Email Scans",
    FeatureType.url: "URL Scans",
    FeatureType.qr: "QR Scans",
    FeatureType.apk: "APK Scans",
  };

  final Map<FeatureType, Color> iconColors = {
    FeatureType.sms: const Color.fromARGB(255, 139, 92, 246),
    FeatureType.email: const Color.fromARGB(255, 59, 130, 246),
    FeatureType.url: const Color.fromARGB(255, 245, 158, 11),
    FeatureType.qr: const Color.fromARGB(255, 99, 102, 241),
    FeatureType.apk: const Color.fromARGB(255, 15, 185, 129),
  };

  final Map<String, FeatureType> scanTypeMapping = {
    'SMS': FeatureType.sms,
    'EMAIL': FeatureType.email,
    'URL': FeatureType.url,
    'QR': FeatureType.qr,
    'APK': FeatureType.apk,
  };

  @override
  Widget build(BuildContext context) {
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    final cardColor = Theme.of(context).cardColor;
    final textColor =
        Theme.of(context).textTheme.bodyMedium?.color ?? Colors.white;
    const Color primaryBlue = Color(0xFF3B82F6);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        title: _buildAppBarWithLogo(textColor, primaryBlue),
        backgroundColor: backgroundColor,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: BlocBuilder<DashboardBloc, DashboardState>(
        builder: (context, dashboardState) {
          return BlocBuilder<AuthBloc, AuthState>(
            builder: (context, authState) {
              return RefreshIndicator(
                onRefresh: () async {
                  context.read<DashboardBloc>().add(
                    const DashboardDataRefreshed(),
                  );
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome Message with User Name
                      _buildWelcomeMessage(authState, textColor),
                      const SizedBox(height: 32),

                      // Dashboard Content
                      if (dashboardState.isLoading &&
                          dashboardState.dashboardStats == null)
                        _buildLoadingState()
                      else if (dashboardState.hasError)
                        _buildErrorState(dashboardState.errorMessage!, context)
                      else
                        _buildDashboardContent(
                          dashboardState,
                          cardColor,
                          textColor,
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: FloatingActionButton(
          onPressed: () => _showFeatureMenu(context),
          backgroundColor: const Color.fromARGB(255, 139, 92, 246),
          tooltip: "Scan Features",
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(
            Icons.security_outlined,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  // Welcome message with dynamic user name
  Widget _buildWelcomeMessage(AuthState authState, Color textColor) {
    final userName = authState.user?.name ?? 'User';
    return Text(
      'Hello, $userName 👋',
      style: TextStyle(
        color: textColor,
        fontSize: 24,
        fontWeight: FontWeight.w600,
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.3, end: 0);
  }

  // Loading state
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Color.fromARGB(255, 139, 92, 246),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading dashboard data...',
            style: TextStyle(color: Colors.grey[400], fontSize: 16),
          ),
        ],
      ),
    );
  }

  // Error state
  Widget _buildErrorState(String errorMessage, BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'Failed to load dashboard data',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<DashboardBloc>().add(const DashboardDataRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color.fromARGB(255, 139, 92, 246),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Main dashboard content
  Widget _buildDashboardContent(
    DashboardState dashboardState,
    Color cardColor,
    Color textColor,
  ) {
    final stats = dashboardState.dashboardStats!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Recent Scans Section
        Text(
          'Recent Scans',
          style: TextStyle(
            color: textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        _buildRecentScansSection(stats, cardColor, textColor),
      ],
    );
  }

  // Recent scans section
  Widget _buildRecentScansSection(
    DashboardStats stats,
    Color cardColor,
    Color textColor,
  ) {
    if (stats.recentScans.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24),
        ),
        child: Column(
          children: [
            Icon(Icons.history, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No recent scans',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start scanning to see your recent activity here',
              style: TextStyle(color: Colors.grey[500], fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children:
          stats.recentScans.take(5).map((scan) {
            final scanType =
                scanTypeMapping.entries
                    .firstWhere(
                      (entry) =>
                          entry.key.toLowerCase() ==
                          scan.scanType.toLowerCase(),
                      orElse: () => const MapEntry('UNKNOWN', FeatureType.sms),
                    )
                    .value;
            final iconColor = iconColors[scanType]!;
            final threatColor = _getThreatColor(scan.result.threatLevel);

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white24),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: iconColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      featureIcons[scanType],
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          scan.scanType,
                          style: TextStyle(
                            color: textColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          scan.target.length > 30
                              ? '${scan.target.substring(0, 30)}...'
                              : scan.target,
                          style: TextStyle(
                            color: textColor.withValues(alpha: 0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: threatColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      scan.result.threatLevel,
                      style: TextStyle(
                        color: threatColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Color _getThreatColor(String threatLevel) {
    switch (threatLevel.toUpperCase()) {
      case 'LOW':
        return Colors.green;
      case 'MEDIUM':
        return Colors.orange;
      case 'HIGH':
        return Colors.red;
      case 'CRITICAL':
        return Colors.red[900]!;
      default:
        return Colors.grey;
    }
  }

  Widget _buildAppBarWithLogo(Color textColor, Color iconColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Left side - empty space for balance
        const SizedBox(width: 48),

        // Center - DarkOps Logo
        Expanded(
          child: Center(
            child: Image.asset(
              'images/darkopslogo.png',
              height: 32,
              fit: BoxFit.contain,
            ),
          ),
        ),

        // Right side - Profile Menu
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Icon(Icons.person_outlined, color: iconColor, size: 24),
          ),
          color: Theme.of(context).scaffoldBackgroundColor,
          onSelected: (value) {
            switch (value) {
              case 'account':
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Account selected')),
                );
                break;
              case 'logout':
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => const LoginOptions()),
                );
                break;
            }
          },
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'account',
                  child: Row(
                    children: [
                      Icon(
                        Icons.account_circle_outlined,
                        color: textColor,
                        size: 20,
                      ),
                      const SizedBox(width: 10),
                      const Text('Account'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'light_mode',
                  child: Consumer<ThemeProvider>(
                    builder:
                        (context, themeProvider, _) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  themeProvider.isDarkMode
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                                  color: textColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 10),
                                const Text('Dark Mode'),
                              ],
                            ),
                            Switch(
                              value: themeProvider.isDarkMode,
                              onChanged: (value) {
                                themeProvider.toggleTheme(value);
                              },
                              activeColor: Colors.white,
                            ),
                          ],
                        ),
                  ),
                ),
                PopupMenuItem(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout_outlined, color: textColor, size: 20),
                      const SizedBox(width: 10),
                      const Text('Logout'),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  void _showFeatureMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ...FeatureType.values.toList().asMap().entries.map(
                (entry) => _buildAnimatedFeatureTile(entry.key, entry.value),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnimatedFeatureTile(int index, FeatureType type) {
    final iconColor = iconColors[type]!;

    return ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          leading: Icon(featureIcons[type]!, color: iconColor),
          title: Text(
            featureTitles[type]!,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          onTap: () {
            Navigator.pop(context);
            switch (type) {
              case FeatureType.qr:
                _openQRScanner();
                break;
              case FeatureType.sms:
                _openSMSAnalyzer();
                break;
              case FeatureType.email:
                _openEmailAnalyzer();
                break;
              case FeatureType.url:
                _openURLAnalyzer();
                break;
              case FeatureType.apk:
                _openAPKAnalyzer();
                break;
            }
          },
        )
        .animate(delay: (index * 100).ms)
        .fade(duration: 300.ms)
        .slideX(begin: 0.3, end: 0);
  }

  void _openQRScanner() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const QRScannerPage()),
    );

    if (result != null && mounted) {
      // Refresh dashboard data after scan
      context.read<DashboardBloc>().add(const DashboardDataRefreshed());

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Scanned QR: $result')));
    }
  }

  void _openSMSAnalyzer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SMSAnalyzerPage()),
    );

    if (result != null && mounted) {
      // Refresh dashboard data after scan
      context.read<DashboardBloc>().add(const DashboardDataRefreshed());
    }
  }

  void _openURLAnalyzer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const URLScannerPage()),
    );

    if (result != null && mounted) {
      // Refresh dashboard data after scan
      context.read<DashboardBloc>().add(const DashboardDataRefreshed());
    }
  }

  void _openAPKAnalyzer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const APKAnalyzerPage()),
    );

    if (result != null && mounted) {
      // Refresh dashboard data after scan
      context.read<DashboardBloc>().add(const DashboardDataRefreshed());
    }
  }

  void _openEmailAnalyzer() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EmailAnalysisPage()),
    );

    if (result != null && mounted) {
      // Refresh dashboard data after scan
      context.read<DashboardBloc>().add(const DashboardDataRefreshed());
    }
  }
}
